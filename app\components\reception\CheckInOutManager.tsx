"use client";

import { useState, useEffect } from "react";
import { Calendar, Clock, User, MapPin, Phone, Mail, CheckCircle, XCircle, AlertCircle } from "lucide-react";
import { useToast } from "@/app/hooks/useNotifications";
import ErrorBoundary from "@/app/components/dashboard/ErrorBoundary";
import { LoadingSkeleton } from "@/app/components/ui/SkeletonComponents";

interface Guest {
  id: string;
  userEmail: string;
  userName?: string;
  userPhone?: string;
  checkIn: string;
  checkOut?: string;
  status: "PENDING" | "CONFIRMED" | "CHECKED_IN" | "CHECKED_OUT" | "CANCELLED";
  resort?: { name: string; location: string } | null;
  spaTreatment?: { name: string; duration: number } | null;
  notes?: string;
  roomNumber?: string;
  specialRequests?: string;
}

interface CheckInOutManagerProps {
  className?: string;
}

export default function CheckInOutManager({ className = "" }: CheckInOutManagerProps) {
  const toast = useToast();
  const [guests, setGuests] = useState<Guest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [processingGuests, setProcessingGuests] = useState<Set<string>>(new Set());
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [filter, setFilter] = useState<"all" | "pending" | "checked_in" | "ready_checkout">("all");

  // Fetch guests for selected date
  const fetchGuests = async () => {
    try {
      setError(null);
      setLoading(true);

      const response = await fetch(`/api/reception/guests?date=${selectedDate}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch guests: ${response.status}`);
      }

      const guestsData = await response.json();
      setGuests(guestsData);
    } catch (err) {
      console.error("Error fetching guests:", err);
      setError(err instanceof Error ? err.message : "Failed to load guests");
    } finally {
      setLoading(false);
    }
  };

  // Handle check-in
  const handleCheckIn = async (guestId: string) => {
    if (processingGuests.has(guestId)) return;

    setProcessingGuests(prev => new Set(prev).add(guestId));

    try {
      const response = await fetch(`/api/reception/checkin/${guestId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: "CHECKED_IN",
          checkInTime: new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to check in guest");
      }

      const updatedGuest = await response.json();
      setGuests(prev => prev.map(guest => 
        guest.id === guestId ? { ...guest, status: "CHECKED_IN" } : guest
      ));

      toast.success("Check-in Successful", `Guest ${updatedGuest.userEmail} has been checked in.`);
    } catch (err) {
      console.error("Error checking in guest:", err);
      toast.error("Check-in Failed", err instanceof Error ? err.message : "Failed to check in guest");
    } finally {
      setProcessingGuests(prev => {
        const newSet = new Set(prev);
        newSet.delete(guestId);
        return newSet;
      });
    }
  };

  // Handle check-out
  const handleCheckOut = async (guestId: string) => {
    if (processingGuests.has(guestId)) return;

    setProcessingGuests(prev => new Set(prev).add(guestId));

    try {
      const response = await fetch(`/api/reception/checkout/${guestId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: "CHECKED_OUT",
          checkOutTime: new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to check out guest");
      }

      const updatedGuest = await response.json();
      setGuests(prev => prev.map(guest => 
        guest.id === guestId ? { ...guest, status: "CHECKED_OUT" } : guest
      ));

      toast.success("Check-out Successful", `Guest ${updatedGuest.userEmail} has been checked out.`);
    } catch (err) {
      console.error("Error checking out guest:", err);
      toast.error("Check-out Failed", err instanceof Error ? err.message : "Failed to check out guest");
    } finally {
      setProcessingGuests(prev => {
        const newSet = new Set(prev);
        newSet.delete(guestId);
        return newSet;
      });
    }
  };

  // Filter guests based on selected filter
  const filteredGuests = guests.filter(guest => {
    switch (filter) {
      case "pending":
        return guest.status === "PENDING" || guest.status === "CONFIRMED";
      case "checked_in":
        return guest.status === "CHECKED_IN";
      case "ready_checkout":
        return guest.status === "CHECKED_IN" && new Date(guest.checkOut || "") <= new Date();
      default:
        return true;
    }
  });

  // Get status badge
  const getStatusBadge = (status: Guest["status"]) => {
    switch (status) {
      case "PENDING":
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <Clock className="w-3 h-3 mr-1" />
            Pending
          </span>
        );
      case "CONFIRMED":
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <CheckCircle className="w-3 h-3 mr-1" />
            Confirmed
          </span>
        );
      case "CHECKED_IN":
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircle className="w-3 h-3 mr-1" />
            Checked In
          </span>
        );
      case "CHECKED_OUT":
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            <XCircle className="w-3 h-3 mr-1" />
            Checked Out
          </span>
        );
      case "CANCELLED":
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <XCircle className="w-3 h-3 mr-1" />
            Cancelled
          </span>
        );
      default:
        return null;
    }
  };

  useEffect(() => {
    fetchGuests();
  }, [selectedDate]);

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
        <LoadingSkeleton count={5} />
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className={`bg-white rounded-lg shadow-sm border ${className}`}>
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Check-in / Check-out Manager</h2>
              <p className="text-gray-600 mt-1">Manage guest arrivals and departures</p>
            </div>
            <div className="flex items-center space-x-4">
              <div>
                <label htmlFor="date-select" className="block text-sm font-medium text-gray-700 mb-1">
                  Date
                </label>
                <input
                  id="date-select"
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label htmlFor="filter-select" className="block text-sm font-medium text-gray-700 mb-1">
                  Filter
                </label>
                <select
                  id="filter-select"
                  value={filter}
                  onChange={(e) => setFilter(e.target.value as any)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Guests</option>
                  <option value="pending">Pending Check-in</option>
                  <option value="checked_in">Checked In</option>
                  <option value="ready_checkout">Ready for Check-out</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {error ? (
            <div className="text-center py-8">
              <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Guests</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <button
                onClick={fetchGuests}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
              >
                Try Again
              </button>
            </div>
          ) : filteredGuests.length === 0 ? (
            <div className="text-center py-8">
              <User className="w-12 h-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Guests Found</h3>
              <p className="text-gray-600">No guests found for the selected date and filter.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredGuests.map((guest) => (
                <div
                  key={guest.id}
                  className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                          <User className="w-5 h-5 text-gray-500" />
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900">
                            {guest.userName || guest.userEmail}
                          </h3>
                          <div className="flex items-center space-x-4 text-sm text-gray-600">
                            <div className="flex items-center">
                              <Mail className="w-4 h-4 mr-1" />
                              {guest.userEmail}
                            </div>
                            {guest.userPhone && (
                              <div className="flex items-center">
                                <Phone className="w-4 h-4 mr-1" />
                                {guest.userPhone}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-3">
                        <div className="flex items-center text-sm text-gray-600">
                          <Calendar className="w-4 h-4 mr-2" />
                          <span>Check-in: {new Date(guest.checkIn).toLocaleDateString()}</span>
                        </div>
                        {guest.checkOut && (
                          <div className="flex items-center text-sm text-gray-600">
                            <Calendar className="w-4 h-4 mr-2" />
                            <span>Check-out: {new Date(guest.checkOut).toLocaleDateString()}</span>
                          </div>
                        )}
                        {(guest.resort || guest.spaTreatment) && (
                          <div className="flex items-center text-sm text-gray-600">
                            <MapPin className="w-4 h-4 mr-2" />
                            <span>
                              {guest.resort?.name || guest.spaTreatment?.name}
                              {guest.roomNumber && ` - Room ${guest.roomNumber}`}
                            </span>
                          </div>
                        )}
                      </div>

                      {(guest.notes || guest.specialRequests) && (
                        <div className="text-sm text-gray-600 mb-3">
                          <strong>Notes:</strong> {guest.notes || guest.specialRequests}
                        </div>
                      )}

                      <div className="flex items-center justify-between">
                        <div>{getStatusBadge(guest.status)}</div>
                        <div className="flex items-center space-x-2">
                          {(guest.status === "PENDING" || guest.status === "CONFIRMED") && (
                            <button
                              onClick={() => handleCheckIn(guest.id)}
                              disabled={processingGuests.has(guest.id)}
                              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center"
                            >
                              <CheckCircle className="w-4 h-4 mr-2" />
                              {processingGuests.has(guest.id) ? "Processing..." : "Check In"}
                            </button>
                          )}
                          {guest.status === "CHECKED_IN" && (
                            <button
                              onClick={() => handleCheckOut(guest.id)}
                              disabled={processingGuests.has(guest.id)}
                              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center"
                            >
                              <XCircle className="w-4 h-4 mr-2" />
                              {processingGuests.has(guest.id) ? "Processing..." : "Check Out"}
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </ErrorBoundary>
  );
}
