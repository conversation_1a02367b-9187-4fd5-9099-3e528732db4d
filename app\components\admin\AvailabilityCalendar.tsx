"use client";

import { useState, useEffect } from "react";
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isToday, isBefore, startOfDay } from "date-fns";

interface Booking {
  id: string;
  checkIn: Date;
  checkOut: Date;
  status: "PENDING" | "CONFIRMED" | "CANCELLED" | "COMPLETED";
  userEmail: string;
}

interface AvailabilityCalendarProps {
  roomId?: string;
  resortId?: string;
  className?: string;
}

export default function AvailabilityCalendar({
  roomId,
  resortId,
  className = ""
}: AvailabilityCalendarProps) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch bookings for the current month
  useEffect(() => {
    const fetchBookings = async () => {
      try {
        setIsLoading(true);
        const startDate = startOfMonth(currentDate);
        const endDate = endOfMonth(currentDate);
        
        const params = new URLSearchParams({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
        });
        
        if (roomId) params.append('roomId', roomId);
        if (resortId) params.append('resortId', resortId);

        const response = await fetch(`/api/admin/bookings/calendar?${params}`);
        if (response.ok) {
          const data = await response.json();
          setBookings(data.map((booking: any) => ({
            ...booking,
            checkIn: new Date(booking.checkIn),
            checkOut: new Date(booking.checkOut)
          })));
          setError(null);
        } else {
          throw new Error("Failed to fetch bookings");
        }
      } catch (err) {
        console.error("Error fetching bookings:", err);
        setError("Failed to load calendar data");
      } finally {
        setIsLoading(false);
      }
    };

    fetchBookings();
  }, [currentDate, roomId, resortId]);

  // Get days for the current month
  const monthStart = startOfMonth(currentDate);
  const monthEnd = endOfMonth(currentDate);
  const days = eachDayOfInterval({ start: monthStart, end: monthEnd });

  // Check if a day has bookings
  const getDayBookings = (day: Date) => {
    return bookings.filter(booking => {
      const dayStart = startOfDay(day);
      const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000);
      
      return (
        (booking.checkIn <= dayStart && booking.checkOut > dayStart) ||
        (booking.checkIn >= dayStart && booking.checkIn < dayEnd)
      );
    });
  };

  // Get day status
  const getDayStatus = (day: Date) => {
    const dayBookings = getDayBookings(day);
    const isPast = isBefore(day, startOfDay(new Date()));
    
    if (isPast) return "past";
    if (dayBookings.length === 0) return "available";
    
    const hasConfirmed = dayBookings.some(b => b.status === "CONFIRMED");
    const hasPending = dayBookings.some(b => b.status === "PENDING");
    
    if (hasConfirmed) return "booked";
    if (hasPending) return "pending";
    return "available";
  };

  // Navigation functions
  const goToPreviousMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1));
  };

  const goToNextMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1));
  };

  const goToToday = () => {
    setCurrentDate(new Date());
  };

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-7 gap-2">
            {Array.from({ length: 35 }).map((_, i) => (
              <div key={i} className="h-10 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
        <div className="text-center py-8">
          <div className="text-red-400 text-4xl mb-4">⚠️</div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Calendar</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            type="button"
            onClick={() => window.location.reload()}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">
          Availability Calendar
        </h3>
        <div className="flex items-center space-x-2">
          <button
            type="button"
            onClick={goToPreviousMonth}
            className="p-2 hover:bg-gray-100 rounded-md transition-colors"
          >
            ←
          </button>
          <span className="text-lg font-medium text-gray-900 min-w-[150px] text-center">
            {format(currentDate, "MMMM yyyy")}
          </span>
          <button
            type="button"
            onClick={goToNextMonth}
            className="p-2 hover:bg-gray-100 rounded-md transition-colors"
          >
            →
          </button>
          <button
            type="button"
            onClick={goToToday}
            className="ml-4 px-3 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors text-sm"
          >
            Today
          </button>
        </div>
      </div>

      {/* Legend */}
      <div className="flex items-center justify-center space-x-6 mb-4 text-sm">
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-green-100 border border-green-300 rounded"></div>
          <span className="text-gray-600">Available</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-yellow-100 border border-yellow-300 rounded"></div>
          <span className="text-gray-600">Pending</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-red-100 border border-red-300 rounded"></div>
          <span className="text-gray-600">Booked</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-gray-100 border border-gray-300 rounded"></div>
          <span className="text-gray-600">Past</span>
        </div>
      </div>

      {/* Calendar Grid */}
      <div className="grid grid-cols-7 gap-1">
        {/* Day headers */}
        {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day) => (
          <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
            {day}
          </div>
        ))}

        {/* Calendar days */}
        {days.map((day) => {
          const status = getDayStatus(day);
          const dayBookings = getDayBookings(day);
          const isCurrentMonth = isSameMonth(day, currentDate);
          const isTodayDate = isToday(day);

          let dayClasses = "p-2 text-center text-sm border rounded cursor-pointer transition-colors ";
          
          if (!isCurrentMonth) {
            dayClasses += "text-gray-300 bg-gray-50 ";
          } else {
            switch (status) {
              case "available":
                dayClasses += "bg-green-50 border-green-200 text-green-800 hover:bg-green-100 ";
                break;
              case "pending":
                dayClasses += "bg-yellow-50 border-yellow-200 text-yellow-800 hover:bg-yellow-100 ";
                break;
              case "booked":
                dayClasses += "bg-red-50 border-red-200 text-red-800 hover:bg-red-100 ";
                break;
              case "past":
                dayClasses += "bg-gray-50 border-gray-200 text-gray-500 ";
                break;
            }
          }

          if (isTodayDate) {
            dayClasses += "ring-2 ring-blue-500 ";
          }

          return (
            <div
              key={day.toISOString()}
              className={dayClasses}
              title={`${format(day, "MMM d, yyyy")} - ${dayBookings.length} booking(s)`}
            >
              <div className="font-medium">{format(day, "d")}</div>
              {dayBookings.length > 0 && (
                <div className="text-xs mt-1">
                  {dayBookings.length}
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Summary */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-green-600">
              {days.filter(day => getDayStatus(day) === "available").length}
            </div>
            <div className="text-sm text-gray-600">Available Days</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-yellow-600">
              {days.filter(day => getDayStatus(day) === "pending").length}
            </div>
            <div className="text-sm text-gray-600">Pending Days</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-red-600">
              {days.filter(day => getDayStatus(day) === "booked").length}
            </div>
            <div className="text-sm text-gray-600">Booked Days</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-blue-600">
              {bookings.length}
            </div>
            <div className="text-sm text-gray-600">Total Bookings</div>
          </div>
        </div>
      </div>
    </div>
  );
}
