import { prisma } from "@/lib/prisma";
import { Suspense } from "react";
import Link from "next/link";
import Image from "next/image";
import {
  GridSkeleton,
  AdminCardSkeleton,
} from "@/app/components/ui/SkeletonComponents";
import { LoadingError, EmptyState } from "@/app/components/ui/ErrorComponents";
import ErrorBoundary from "@/app/components/dashboard/ErrorBoundary";

// TypeScript interfaces
interface Event {
  id: string;
  name: string;
  description: string;
  price?: number;
  image: string;
  images: string[];
  category?: string;
  features: string[];
  venue?: string;
  maxCapacity?: number;
  duration?: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface AdminEventsProps {
  searchParams?: {
    category?: string;
    active?: string;
  };
}

// Event card component
function EventCard({ event }: { event: Event }) {
  return (
    <div className="bg-white rounded-xl shadow-sm border overflow-hidden hover:shadow-md transition-shadow">
      <div className="relative h-48">
        <Image
          src={event.image}
          alt={event.name}
          fill
          className="object-cover"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
        <div className="absolute top-2 right-2 flex gap-2">
          {event.price ? (
            <span className="bg-indigo-600 text-white px-2 py-1 rounded text-sm font-semibold">
              ETB {event.price}
            </span>
          ) : (
            <span className="bg-green-600 text-white px-2 py-1 rounded text-sm font-semibold">
              Free
            </span>
          )}
          <span className={`px-2 py-1 rounded text-xs font-medium ${
            event.isActive 
              ? "bg-green-100 text-green-800" 
              : "bg-red-100 text-red-800"
          }`}>
            {event.isActive ? "Active" : "Inactive"}
          </span>
        </div>
        {event.duration && (
          <div className="absolute top-2 left-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded">
            {event.duration}h
          </div>
        )}
      </div>

      <div className="p-6">
        <div className="flex items-start justify-between mb-2">
          <h3 className="text-lg font-semibold text-gray-900 line-clamp-1">
            {event.name}
          </h3>
          {event.category && (
            <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
              {event.category}
            </span>
          )}
        </div>

        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
          {event.description}
        </p>

        <div className="space-y-2 mb-4">
          {event.venue && (
            <div className="flex items-center text-sm text-gray-500">
              <span className="font-medium">Venue:</span>
              <span className="ml-1">{event.venue}</span>
            </div>
          )}
          {event.maxCapacity && (
            <div className="flex items-center text-sm text-gray-500">
              <span className="font-medium">Max Capacity:</span>
              <span className="ml-1">{event.maxCapacity} attendees</span>
            </div>
          )}
        </div>

        {event.features.length > 0 && (
          <div className="mb-4">
            <div className="flex flex-wrap gap-1">
              {event.features.slice(0, 3).map((feature, index) => (
                <span
                  key={index}
                  className="text-xs bg-indigo-50 text-indigo-700 px-2 py-1 rounded"
                >
                  {feature}
                </span>
              ))}
              {event.features.length > 3 && (
                <span className="text-xs text-gray-500">
                  +{event.features.length - 3} more
                </span>
              )}
            </div>
          </div>
        )}

        <div className="flex gap-2">
          <Link
            href={`/admin/events/${event.id}/edit`}
            className="flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors text-center"
          >
            Edit
          </Link>
          <button
            onClick={() => {
              if (confirm("Are you sure you want to delete this event?")) {
                // TODO: Implement delete functionality
                console.log("Delete event:", event.id);
              }
            }}
            className="px-3 py-2 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 transition-colors"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  );
}

// Events grid component
async function EventsGrid() {
  try {
    const events = await prisma.event.findMany({
      orderBy: { createdAt: "desc" },
    });

    if (events.length === 0) {
      return (
        <EmptyState
          title="No Events Created Yet"
          message="Start by adding your first event to showcase special occasions, conferences, and celebrations."
          icon={<div className="text-gray-400 text-6xl mb-4">🎉</div>}
          action={
            <Link
              href="/admin/events/create"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              + Add First Event
            </Link>
          }
        />
      );
    }

    return (
      <div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        role="list"
        aria-label="Events list"
      >
        {events.map((event) => (
          <EventCard key={event.id} event={event} />
        ))}
      </div>
    );
  } catch (error) {
    console.error("Error fetching events:", error);
    return (
      <LoadingError
        resource="events"
        onRetry={() => window.location.reload()}
      />
    );
  }
}

export default async function AdminEvents({ searchParams }: AdminEventsProps) {
  return (
    <ErrorBoundary>
      <div className="p-6 max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Events Management
            </h1>
            <p className="text-gray-600 mt-1">
              Manage special events, conferences, celebrations, and venue bookings
            </p>
          </div>
          <Link
            href="/admin/events/create"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors shadow-sm"
            aria-label="Create new event"
          >
            <span className="mr-2">+</span>
            Add Event
          </Link>
        </div>

        <Suspense
          fallback={
            <GridSkeleton
              count={6}
              columns={3}
              SkeletonComponent={AdminCardSkeleton}
            />
          }
        >
          <EventsGrid />
        </Suspense>
      </div>
    </ErrorBoundary>
  );
}
