import { prisma } from "@/lib/prisma";
import { Suspense } from "react";
import Link from "next/link";
import Image from "next/image";
import {
  GridSkeleton,
  AdminCardSkeleton,
} from "@/app/components/ui/SkeletonComponents";
import { LoadingError, EmptyState } from "@/app/components/ui/ErrorComponents";
import ErrorBoundary from "@/app/components/dashboard/ErrorBoundary";

// TypeScript interfaces
interface Experience {
  id: string;
  name: string;
  description: string;
  price: number;
  duration?: number;
  image: string;
  images: string[];
  category?: string;
  features: string[];
  difficulty?: string;
  minAge?: number;
  maxCapacity?: number;
  location?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface AdminExperiencesProps {
  searchParams?: {
    category?: string;
    difficulty?: string;
    active?: string;
  };
}

// Experience card component
function ExperienceCard({ experience }: { experience: Experience }) {
  return (
    <div className="bg-white rounded-xl shadow-sm border overflow-hidden hover:shadow-md transition-shadow">
      <div className="relative h-48">
        <Image
          src={experience.image}
          alt={experience.name}
          fill
          className="object-cover"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
        <div className="absolute top-2 right-2 flex gap-2">
          <span className="bg-green-600 text-white px-2 py-1 rounded text-sm font-semibold">
            ETB {experience.price}
          </span>
          <span className={`px-2 py-1 rounded text-xs font-medium ${
            experience.isActive 
              ? "bg-green-100 text-green-800" 
              : "bg-red-100 text-red-800"
          }`}>
            {experience.isActive ? "Active" : "Inactive"}
          </span>
        </div>
        {experience.difficulty && (
          <div className="absolute top-2 left-2">
            <span className={`px-2 py-1 rounded text-xs font-medium ${
              experience.difficulty === "Easy" ? "bg-green-100 text-green-800" :
              experience.difficulty === "Moderate" ? "bg-yellow-100 text-yellow-800" :
              experience.difficulty === "Challenging" ? "bg-orange-100 text-orange-800" :
              "bg-red-100 text-red-800"
            }`}>
              {experience.difficulty}
            </span>
          </div>
        )}
      </div>

      <div className="p-6">
        <div className="flex items-start justify-between mb-2">
          <h3 className="text-lg font-semibold text-gray-900 line-clamp-1">
            {experience.name}
          </h3>
          {experience.category && (
            <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
              {experience.category}
            </span>
          )}
        </div>

        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
          {experience.description}
        </p>

        <div className="space-y-2 mb-4">
          {experience.duration && (
            <div className="flex items-center text-sm text-gray-500">
              <span className="font-medium">Duration:</span>
              <span className="ml-1">{experience.duration} minutes</span>
            </div>
          )}
          {experience.location && (
            <div className="flex items-center text-sm text-gray-500">
              <span className="font-medium">Location:</span>
              <span className="ml-1">{experience.location}</span>
            </div>
          )}
          {experience.maxCapacity && (
            <div className="flex items-center text-sm text-gray-500">
              <span className="font-medium">Max Capacity:</span>
              <span className="ml-1">{experience.maxCapacity} people</span>
            </div>
          )}
          {experience.minAge && (
            <div className="flex items-center text-sm text-gray-500">
              <span className="font-medium">Min Age:</span>
              <span className="ml-1">{experience.minAge} years</span>
            </div>
          )}
        </div>

        {experience.features.length > 0 && (
          <div className="mb-4">
            <div className="flex flex-wrap gap-1">
              {experience.features.slice(0, 3).map((feature, index) => (
                <span
                  key={index}
                  className="text-xs bg-blue-50 text-blue-700 px-2 py-1 rounded"
                >
                  {feature}
                </span>
              ))}
              {experience.features.length > 3 && (
                <span className="text-xs text-gray-500">
                  +{experience.features.length - 3} more
                </span>
              )}
            </div>
          </div>
        )}

        <div className="flex gap-2">
          <Link
            href={`/admin/experiences/${experience.id}/edit`}
            className="flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors text-center"
          >
            Edit
          </Link>
          <button
            onClick={() => {
              if (confirm("Are you sure you want to delete this experience?")) {
                // TODO: Implement delete functionality
                console.log("Delete experience:", experience.id);
              }
            }}
            className="px-3 py-2 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 transition-colors"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  );
}

// Experiences grid component
async function ExperiencesGrid() {
  try {
    const experiences = await prisma.experience.findMany({
      orderBy: { createdAt: "desc" },
    });

    if (experiences.length === 0) {
      return (
        <EmptyState
          title="No Experiences Created Yet"
          message="Start by adding your first experience to showcase exciting activities and adventures for your guests."
          icon={<div className="text-gray-400 text-6xl mb-4">🏃‍♂️</div>}
          action={
            <Link
              href="/admin/experiences/create"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              + Add First Experience
            </Link>
          }
        />
      );
    }

    return (
      <div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        role="list"
        aria-label="Experiences list"
      >
        {experiences.map((experience) => (
          <ExperienceCard key={experience.id} experience={experience} />
        ))}
      </div>
    );
  } catch (error) {
    console.error("Error fetching experiences:", error);
    return (
      <LoadingError
        resource="experiences"
        onRetry={() => window.location.reload()}
      />
    );
  }
}

export default async function AdminExperiences({ searchParams }: AdminExperiencesProps) {
  return (
    <ErrorBoundary>
      <div className="p-6 max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Experiences Management
            </h1>
            <p className="text-gray-600 mt-1">
              Manage activities, adventures, and experiences for your guests
            </p>
          </div>
          <Link
            href="/admin/experiences/create"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors shadow-sm"
            aria-label="Create new experience"
          >
            <span className="mr-2">+</span>
            Add Experience
          </Link>
        </div>

        <Suspense
          fallback={
            <GridSkeleton
              count={6}
              columns={3}
              SkeletonComponent={AdminCardSkeleton}
            />
          }
        >
          <ExperiencesGrid />
        </Suspense>
      </div>
    </ErrorBoundary>
  );
}
