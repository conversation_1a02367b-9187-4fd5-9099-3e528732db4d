// Standardized skeleton components for consistent loading states across the application

import React from "react";

// Base skeleton component
interface SkeletonProps {
  className?: string;
  width?: string;
  height?: string;
  rounded?: "none" | "sm" | "md" | "lg" | "xl" | "full";
}

export function Skeleton({ 
  className = "", 
  width = "w-full", 
  height = "h-4", 
  rounded = "md" 
}: SkeletonProps) {
  const roundedClass = {
    none: "",
    sm: "rounded-sm",
    md: "rounded",
    lg: "rounded-lg",
    xl: "rounded-xl",
    full: "rounded-full"
  }[rounded];

  return (
    <div 
      className={`bg-gray-200 animate-pulse ${width} ${height} ${roundedClass} ${className}`}
      aria-hidden="true"
    />
  );
}

// Card skeleton components
interface CardSkeletonProps {
  className?: string;
  showImage?: boolean;
  showActions?: boolean;
  imageHeight?: string;
}

export function CardSkeleton({ 
  className = "", 
  showImage = true, 
  showActions = true,
  imageHeight = "h-48"
}: CardSkeletonProps) {
  return (
    <div className={`bg-white rounded-xl shadow-md overflow-hidden animate-pulse ${className}`}>
      {showImage && (
        <Skeleton width="w-full" height={imageHeight} rounded="none" />
      )}
      <div className="p-6">
        <Skeleton width="w-3/4" height="h-4" className="mb-2" />
        <Skeleton width="w-full" height="h-3" className="mb-4" />
        <Skeleton width="w-1/2" height="h-3" className="mb-4" />
        {showActions && (
          <Skeleton width="w-full" height="h-10" />
        )}
      </div>
    </div>
  );
}

// Resort card skeleton
export function ResortCardSkeleton({ className = "" }: { className?: string }) {
  return (
    <div className={`bg-white rounded-xl shadow animate-pulse ${className}`}>
      <Skeleton width="w-full" height="h-48" rounded="xl" className="rounded-t-xl" />
      <div className="p-4">
        <Skeleton width="w-3/4" height="h-6" className="mx-auto" />
      </div>
    </div>
  );
}

// Spa treatment card skeleton
export function SpaCardSkeleton({ className = "" }: { className?: string }) {
  return (
    <CardSkeleton 
      className={className}
      showImage={true}
      showActions={true}
      imageHeight="h-48"
    />
  );
}

// Admin card skeleton (for admin panels)
export function AdminCardSkeleton({ className = "" }: { className?: string }) {
  return (
    <div className={`border rounded-xl p-4 bg-white shadow-sm animate-pulse ${className}`}>
      <Skeleton width="w-full" height="h-40" className="mb-3" />
      <Skeleton width="w-full" height="h-6" className="mb-2" />
      <Skeleton width="w-3/4" height="h-4" className="mb-3" />
      <div className="flex justify-between">
        <Skeleton width="w-12" height="h-4" />
        <Skeleton width="w-16" height="h-4" />
      </div>
    </div>
  );
}

// Table row skeleton
export function TableRowSkeleton({ 
  columns = 4, 
  className = "" 
}: { 
  columns?: number; 
  className?: string; 
}) {
  return (
    <tr className={`animate-pulse ${className}`}>
      {Array.from({ length: columns }).map((_, index) => (
        <td key={index} className="px-6 py-4">
          <Skeleton width="w-full" height="h-4" />
        </td>
      ))}
    </tr>
  );
}

// List item skeleton
export function ListItemSkeleton({ className = "" }: { className?: string }) {
  return (
    <div className={`border p-4 rounded-md shadow-sm animate-pulse ${className}`}>
      <div className="flex justify-between items-center">
        <div className="flex-1">
          <Skeleton width="w-3/4" height="h-5" className="mb-2" />
          <Skeleton width="w-1/2" height="h-4" className="mb-1" />
          <Skeleton width="w-1/4" height="h-3" />
        </div>
        <div className="ml-4">
          <Skeleton width="w-20" height="h-8" />
        </div>
      </div>
    </div>
  );
}

// Grid skeleton wrapper
interface GridSkeletonProps {
  count?: number;
  columns?: 1 | 2 | 3 | 4 | 6;
  SkeletonComponent: React.ComponentType<{ className?: string }>;
  className?: string;
}

export function GridSkeleton({ 
  count = 6, 
  columns = 3, 
  SkeletonComponent,
  className = "" 
}: GridSkeletonProps) {
  const gridClasses = {
    1: "grid-cols-1",
    2: "grid-cols-1 md:grid-cols-2",
    3: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",
    4: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",
    6: "grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6"
  }[columns];

  return (
    <div className={`grid ${gridClasses} gap-6 ${className}`}>
      {Array.from({ length: count }).map((_, index) => (
        <SkeletonComponent key={index} />
      ))}
    </div>
  );
}

// Dashboard stat card skeleton
export function StatCardSkeleton({ className = "" }: { className?: string }) {
  return (
    <div className={`bg-white rounded-lg shadow-sm border p-6 animate-pulse ${className}`}>
      <div className="flex items-center">
        <Skeleton width="w-12" height="h-12" rounded="lg" />
        <div className="ml-4 flex-1">
          <Skeleton width="w-3/4" height="h-4" className="mb-2" />
          <Skeleton width="w-1/2" height="h-6" />
        </div>
      </div>
    </div>
  );
}

// Form skeleton
export function FormSkeleton({ 
  fields = 4, 
  className = "" 
}: { 
  fields?: number; 
  className?: string; 
}) {
  return (
    <div className={`space-y-6 animate-pulse ${className}`}>
      {Array.from({ length: fields }).map((_, index) => (
        <div key={index}>
          <Skeleton width="w-1/4" height="h-4" className="mb-2" />
          <Skeleton width="w-full" height="h-10" />
        </div>
      ))}
      <div className="flex gap-4 pt-4">
        <Skeleton width="w-24" height="h-10" />
        <Skeleton width="w-20" height="h-10" />
      </div>
    </div>
  );
}

// Navigation skeleton
export function NavSkeleton({ className = "" }: { className?: string }) {
  return (
    <nav className={`animate-pulse ${className}`}>
      <div className="flex items-center justify-between">
        <Skeleton width="w-32" height="h-8" />
        <div className="flex space-x-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <Skeleton key={index} width="w-16" height="h-6" />
          ))}
        </div>
      </div>
    </nav>
  );
}

// Text skeleton for content areas
export function TextSkeleton({ 
  lines = 3, 
  className = "" 
}: { 
  lines?: number; 
  className?: string; 
}) {
  return (
    <div className={`space-y-2 animate-pulse ${className}`}>
      {Array.from({ length: lines }).map((_, index) => (
        <Skeleton 
          key={index} 
          width={index === lines - 1 ? "w-3/4" : "w-full"} 
          height="h-4" 
        />
      ))}
    </div>
  );
}
