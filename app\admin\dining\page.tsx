import { prisma } from "@/lib/prisma";
import { Suspense } from "react";
import Link from "next/link";
import Image from "next/image";
import {
  GridSkeleton,
  AdminCardSkeleton,
} from "@/app/components/ui/SkeletonComponents";
import { LoadingError, EmptyState } from "@/app/components/ui/ErrorComponents";
import ErrorBoundary from "@/app/components/dashboard/ErrorBoundary";

// TypeScript interfaces
interface DiningOption {
  id: string;
  name: string;
  description: string;
  image: string;
  category?: string;
  cuisine?: string;
  priceRange?: string;
  features: string[];
  openingHours?: string;
  location?: string;
  capacity?: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface AdminDiningProps {
  searchParams?: {
    category?: string;
    active?: string;
  };
}

// Dining option card component
function DiningCard({ diningOption }: { diningOption: DiningOption }) {
  return (
    <div className="bg-white rounded-xl shadow-sm border overflow-hidden hover:shadow-md transition-shadow">
      <div className="relative h-48">
        <Image
          src={diningOption.image}
          alt={diningOption.name}
          fill
          className="object-cover"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
        <div className="absolute top-2 right-2 flex gap-2">
          {diningOption.priceRange && (
            <span className="bg-blue-600 text-white px-2 py-1 rounded text-sm font-semibold">
              {diningOption.priceRange}
            </span>
          )}
          <span className={`px-2 py-1 rounded text-xs font-medium ${
            diningOption.isActive 
              ? "bg-green-100 text-green-800" 
              : "bg-red-100 text-red-800"
          }`}>
            {diningOption.isActive ? "Active" : "Inactive"}
          </span>
        </div>
      </div>

      <div className="p-6">
        <div className="flex items-start justify-between mb-2">
          <h3 className="text-lg font-semibold text-gray-900 line-clamp-1">
            {diningOption.name}
          </h3>
          {diningOption.category && (
            <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
              {diningOption.category}
            </span>
          )}
        </div>

        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
          {diningOption.description}
        </p>

        <div className="space-y-2 mb-4">
          {diningOption.cuisine && (
            <div className="flex items-center text-sm text-gray-500">
              <span className="font-medium">Cuisine:</span>
              <span className="ml-1">{diningOption.cuisine}</span>
            </div>
          )}
          {diningOption.location && (
            <div className="flex items-center text-sm text-gray-500">
              <span className="font-medium">Location:</span>
              <span className="ml-1">{diningOption.location}</span>
            </div>
          )}
          {diningOption.capacity && (
            <div className="flex items-center text-sm text-gray-500">
              <span className="font-medium">Capacity:</span>
              <span className="ml-1">{diningOption.capacity} seats</span>
            </div>
          )}
          {diningOption.openingHours && (
            <div className="flex items-center text-sm text-gray-500">
              <span className="font-medium">Hours:</span>
              <span className="ml-1">{diningOption.openingHours}</span>
            </div>
          )}
        </div>

        {diningOption.features.length > 0 && (
          <div className="mb-4">
            <div className="flex flex-wrap gap-1">
              {diningOption.features.slice(0, 3).map((feature, index) => (
                <span
                  key={index}
                  className="text-xs bg-blue-50 text-blue-700 px-2 py-1 rounded"
                >
                  {feature}
                </span>
              ))}
              {diningOption.features.length > 3 && (
                <span className="text-xs text-gray-500">
                  +{diningOption.features.length - 3} more
                </span>
              )}
            </div>
          </div>
        )}

        <div className="flex gap-2">
          <Link
            href={`/admin/dining/${diningOption.id}/edit`}
            className="flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors text-center"
          >
            Edit
          </Link>
          <button
            onClick={() => {
              if (confirm("Are you sure you want to delete this dining option?")) {
                // TODO: Implement delete functionality
                console.log("Delete dining option:", diningOption.id);
              }
            }}
            className="px-3 py-2 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 transition-colors"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  );
}

// Dining options grid component
async function DiningGrid() {
  try {
    const diningOptions = await prisma.diningOption.findMany({
      orderBy: { createdAt: "desc" },
    });

    if (diningOptions.length === 0) {
      return (
        <EmptyState
          title="No Dining Options Created Yet"
          message="Start by adding your first dining option to showcase your restaurants, cafes, and bars."
          icon={<div className="text-gray-400 text-6xl mb-4">🍽️</div>}
          action={
            <Link
              href="/admin/dining/create"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              + Add First Dining Option
            </Link>
          }
        />
      );
    }

    return (
      <div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        role="list"
        aria-label="Dining options list"
      >
        {diningOptions.map((diningOption) => (
          <DiningCard key={diningOption.id} diningOption={diningOption} />
        ))}
      </div>
    );
  } catch (error) {
    console.error("Error fetching dining options:", error);
    return (
      <LoadingError
        resource="dining options"
        onRetry={() => window.location.reload()}
      />
    );
  }
}

export default async function AdminDining({ searchParams }: AdminDiningProps) {
  return (
    <ErrorBoundary>
      <div className="p-6 max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Dining Options Management
            </h1>
            <p className="text-gray-600 mt-1">
              Manage your restaurants, cafes, bars, and dining services
            </p>
          </div>
          <Link
            href="/admin/dining/create"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors shadow-sm"
            aria-label="Create new dining option"
          >
            <span className="mr-2">+</span>
            Add Dining Option
          </Link>
        </div>

        <Suspense
          fallback={
            <GridSkeleton
              count={6}
              columns={3}
              SkeletonComponent={AdminCardSkeleton}
            />
          }
        >
          <DiningGrid />
        </Suspense>
      </div>
    </ErrorBoundary>
  );
}
