"use client";

import { useState, useEffect } from "react";
import { Calendar, Clock, User, MapPin, Plus, Edit, Trash2, AlertCircle } from "lucide-react";
import { useToast } from "@/app/hooks/useNotifications";
import ErrorBoundary from "@/app/components/dashboard/ErrorBoundary";
import { LoadingSkeleton } from "@/app/components/ui/SkeletonComponents";

interface ServiceAppointment {
  id: string;
  customerEmail: string;
  customerName?: string;
  serviceName: string;
  serviceType: "spa" | "resort" | "activity";
  startTime: string;
  endTime: string;
  duration: number;
  status: "scheduled" | "in_progress" | "completed" | "cancelled";
  staffMember?: string;
  location?: string;
  notes?: string;
  specialRequests?: string;
}

interface ServiceSchedulerProps {
  className?: string;
}

export default function ServiceScheduler({ className = "" }: ServiceSchedulerProps) {
  const toast = useToast();
  const [appointments, setAppointments] = useState<ServiceAppointment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [viewMode, setViewMode] = useState<"day" | "week">("day");
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingAppointment, setEditingAppointment] = useState<ServiceAppointment | null>(null);

  // Fetch appointments for selected date
  const fetchAppointments = async () => {
    try {
      setError(null);
      setLoading(true);

      const response = await fetch(`/api/reception/schedule?date=${selectedDate}&view=${viewMode}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch appointments: ${response.status}`);
      }

      const appointmentsData = await response.json();
      setAppointments(appointmentsData);
    } catch (err) {
      console.error("Error fetching appointments:", err);
      setError(err instanceof Error ? err.message : "Failed to load appointments");
    } finally {
      setLoading(false);
    }
  };

  // Update appointment status
  const updateAppointmentStatus = async (appointmentId: string, status: ServiceAppointment["status"]) => {
    try {
      const response = await fetch(`/api/reception/schedule/${appointmentId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status }),
      });

      if (!response.ok) {
        throw new Error("Failed to update appointment status");
      }

      setAppointments(prev => prev.map(apt => 
        apt.id === appointmentId ? { ...apt, status } : apt
      ));

      toast.success("Status Updated", `Appointment status changed to ${status.replace('_', ' ')}.`);
    } catch (err) {
      console.error("Error updating appointment status:", err);
      toast.error("Update Failed", err instanceof Error ? err.message : "Failed to update status");
    }
  };

  // Get status badge
  const getStatusBadge = (status: ServiceAppointment["status"]) => {
    switch (status) {
      case "scheduled":
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <Clock className="w-3 h-3 mr-1" />
            Scheduled
          </span>
        );
      case "in_progress":
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <Clock className="w-3 h-3 mr-1" />
            In Progress
          </span>
        );
      case "completed":
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <Clock className="w-3 h-3 mr-1" />
            Completed
          </span>
        );
      case "cancelled":
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <Clock className="w-3 h-3 mr-1" />
            Cancelled
          </span>
        );
      default:
        return null;
    }
  };

  // Get service type badge
  const getServiceTypeBadge = (type: ServiceAppointment["serviceType"]) => {
    switch (type) {
      case "spa":
        return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">Spa</span>;
      case "resort":
        return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Resort</span>;
      case "activity":
        return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">Activity</span>;
      default:
        return null;
    }
  };

  // Generate time slots for the day
  const generateTimeSlots = () => {
    const slots = [];
    for (let hour = 8; hour < 20; hour++) {
      slots.push(`${hour.toString().padStart(2, '0')}:00`);
      slots.push(`${hour.toString().padStart(2, '0')}:30`);
    }
    return slots;
  };

  // Get appointments for a specific time slot
  const getAppointmentsForTimeSlot = (timeSlot: string) => {
    return appointments.filter(apt => {
      const startTime = new Date(apt.startTime).toTimeString().slice(0, 5);
      return startTime === timeSlot;
    });
  };

  useEffect(() => {
    fetchAppointments();
  }, [selectedDate, viewMode]);

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
        <LoadingSkeleton count={8} />
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className={`bg-white rounded-lg shadow-sm border ${className}`}>
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Service Scheduler</h2>
              <p className="text-gray-600 mt-1">Manage service appointments and availability</p>
            </div>
            <div className="flex items-center space-x-4">
              <div>
                <label htmlFor="date-select" className="block text-sm font-medium text-gray-700 mb-1">
                  Date
                </label>
                <input
                  id="date-select"
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label htmlFor="view-select" className="block text-sm font-medium text-gray-700 mb-1">
                  View
                </label>
                <select
                  id="view-select"
                  value={viewMode}
                  onChange={(e) => setViewMode(e.target.value as "day" | "week")}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="day">Day View</option>
                  <option value="week">Week View</option>
                </select>
              </div>
              <button
                onClick={() => setShowAddModal(true)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Appointment
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {error ? (
            <div className="text-center py-8">
              <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Schedule</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <button
                onClick={fetchAppointments}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
              >
                Try Again
              </button>
            </div>
          ) : viewMode === "day" ? (
            <div className="space-y-2">
              {generateTimeSlots().map((timeSlot) => {
                const slotAppointments = getAppointmentsForTimeSlot(timeSlot);
                return (
                  <div key={timeSlot} className="flex border-b border-gray-100 py-2">
                    <div className="w-20 text-sm font-medium text-gray-600 py-2">
                      {timeSlot}
                    </div>
                    <div className="flex-1 min-h-[60px]">
                      {slotAppointments.length === 0 ? (
                        <div className="h-full flex items-center text-gray-400 text-sm">
                          Available
                        </div>
                      ) : (
                        <div className="space-y-2">
                          {slotAppointments.map((appointment) => (
                            <div
                              key={appointment.id}
                              className="bg-blue-50 border border-blue-200 rounded-lg p-3"
                            >
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <div className="flex items-center space-x-2 mb-1">
                                    <h4 className="font-medium text-gray-900">
                                      {appointment.serviceName}
                                    </h4>
                                    {getServiceTypeBadge(appointment.serviceType)}
                                    {getStatusBadge(appointment.status)}
                                  </div>
                                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                                    <div className="flex items-center">
                                      <User className="w-4 h-4 mr-1" />
                                      {appointment.customerName || appointment.customerEmail}
                                    </div>
                                    <div className="flex items-center">
                                      <Clock className="w-4 h-4 mr-1" />
                                      {appointment.duration} min
                                    </div>
                                    {appointment.location && (
                                      <div className="flex items-center">
                                        <MapPin className="w-4 h-4 mr-1" />
                                        {appointment.location}
                                      </div>
                                    )}
                                  </div>
                                  {appointment.staffMember && (
                                    <div className="text-sm text-gray-600 mt-1">
                                      Staff: {appointment.staffMember}
                                    </div>
                                  )}
                                </div>
                                <div className="flex items-center space-x-1 ml-4">
                                  {appointment.status === "scheduled" && (
                                    <button
                                      onClick={() => updateAppointmentStatus(appointment.id, "in_progress")}
                                      className="px-2 py-1 text-xs bg-yellow-600 text-white rounded hover:bg-yellow-700 transition-colors"
                                    >
                                      Start
                                    </button>
                                  )}
                                  {appointment.status === "in_progress" && (
                                    <button
                                      onClick={() => updateAppointmentStatus(appointment.id, "completed")}
                                      className="px-2 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
                                    >
                                      Complete
                                    </button>
                                  )}
                                  <button
                                    onClick={() => setEditingAppointment(appointment)}
                                    className="p-1 text-gray-400 hover:text-gray-600"
                                    aria-label="Edit appointment"
                                  >
                                    <Edit className="w-4 h-4" />
                                  </button>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-8">
              <Calendar className="w-12 h-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Week View</h3>
              <p className="text-gray-600">Week view coming soon...</p>
            </div>
          )}
        </div>
      </div>
    </ErrorBoundary>
  );
}
