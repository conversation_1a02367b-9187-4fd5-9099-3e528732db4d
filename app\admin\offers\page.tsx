import { prisma } from "@/lib/prisma";
import { Suspense } from "react";
import Link from "next/link";
import Image from "next/image";
import {
  GridSkeleton,
  AdminCardSkeleton,
} from "@/app/components/ui/SkeletonComponents";
import { LoadingError, EmptyState } from "@/app/components/ui/ErrorComponents";
import ErrorBoundary from "@/app/components/dashboard/ErrorBoundary";

// TypeScript interfaces
interface SpecialOffer {
  id: string;
  name: string;
  description: string;
  image: string;
  images: string[];
  category?: string;
  originalPrice?: number;
  discountPrice?: number;
  discountPercent?: number;
  features: string[];
  validFrom?: Date;
  validUntil?: Date;
  terms?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface AdminOffersProps {
  searchParams?: {
    category?: string;
    active?: string;
  };
}

// Offer card component
function OfferCard({ offer }: { offer: SpecialOffer }) {
  const isExpired = offer.validUntil && new Date(offer.validUntil) < new Date();
  const isUpcoming = offer.validFrom && new Date(offer.validFrom) > new Date();

  return (
    <div className="bg-white rounded-xl shadow-sm border overflow-hidden hover:shadow-md transition-shadow">
      <div className="relative h-48">
        <Image
          src={offer.image}
          alt={offer.name}
          fill
          className="object-cover"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
        <div className="absolute top-2 right-2 flex gap-2">
          {offer.discountPercent && (
            <span className="bg-red-600 text-white px-2 py-1 rounded text-sm font-semibold">
              {offer.discountPercent}% OFF
            </span>
          )}
          <span className={`px-2 py-1 rounded text-xs font-medium ${
            offer.isActive && !isExpired
              ? "bg-green-100 text-green-800" 
              : "bg-red-100 text-red-800"
          }`}>
            {isExpired ? "Expired" : isUpcoming ? "Upcoming" : offer.isActive ? "Active" : "Inactive"}
          </span>
        </div>
      </div>

      <div className="p-6">
        <div className="flex items-start justify-between mb-2">
          <h3 className="text-lg font-semibold text-gray-900 line-clamp-1">
            {offer.name}
          </h3>
          {offer.category && (
            <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
              {offer.category}
            </span>
          )}
        </div>

        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
          {offer.description}
        </p>

        {(offer.originalPrice || offer.discountPrice) && (
          <div className="mb-3">
            <div className="flex items-center gap-2">
              {offer.originalPrice && offer.discountPrice && (
                <span className="text-gray-500 line-through text-sm">
                  ETB {offer.originalPrice}
                </span>
              )}
              {offer.discountPrice && (
                <span className="text-green-600 font-semibold">
                  ETB {offer.discountPrice}
                </span>
              )}
              {offer.originalPrice && !offer.discountPrice && (
                <span className="text-gray-900 font-semibold">
                  ETB {offer.originalPrice}
                </span>
              )}
            </div>
          </div>
        )}

        <div className="space-y-2 mb-4">
          {offer.validFrom && (
            <div className="flex items-center text-sm text-gray-500">
              <span className="font-medium">Valid From:</span>
              <span className="ml-1">{new Date(offer.validFrom).toLocaleDateString()}</span>
            </div>
          )}
          {offer.validUntil && (
            <div className="flex items-center text-sm text-gray-500">
              <span className="font-medium">Valid Until:</span>
              <span className="ml-1">{new Date(offer.validUntil).toLocaleDateString()}</span>
            </div>
          )}
        </div>

        {offer.features.length > 0 && (
          <div className="mb-4">
            <div className="flex flex-wrap gap-1">
              {offer.features.slice(0, 3).map((feature, index) => (
                <span
                  key={index}
                  className="text-xs bg-orange-50 text-orange-700 px-2 py-1 rounded"
                >
                  {feature}
                </span>
              ))}
              {offer.features.length > 3 && (
                <span className="text-xs text-gray-500">
                  +{offer.features.length - 3} more
                </span>
              )}
            </div>
          </div>
        )}

        <div className="flex gap-2">
          <Link
            href={`/admin/offers/${offer.id}/edit`}
            className="flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors text-center"
          >
            Edit
          </Link>
          <button
            onClick={() => {
              if (confirm("Are you sure you want to delete this special offer?")) {
                // TODO: Implement delete functionality
                console.log("Delete offer:", offer.id);
              }
            }}
            className="px-3 py-2 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 transition-colors"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  );
}

// Offers grid component
async function OffersGrid() {
  try {
    const offers = await prisma.specialOffer.findMany({
      orderBy: { createdAt: "desc" },
    });

    if (offers.length === 0) {
      return (
        <EmptyState
          title="No Special Offers Created Yet"
          message="Start by adding your first special offer to showcase packages, discounts, and seasonal deals."
          icon={<div className="text-gray-400 text-6xl mb-4">🎁</div>}
          action={
            <Link
              href="/admin/offers/create"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              + Add First Offer
            </Link>
          }
        />
      );
    }

    return (
      <div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        role="list"
        aria-label="Special offers list"
      >
        {offers.map((offer) => (
          <OfferCard key={offer.id} offer={offer} />
        ))}
      </div>
    );
  } catch (error) {
    console.error("Error fetching special offers:", error);
    return (
      <LoadingError
        resource="special offers"
        onRetry={() => window.location.reload()}
      />
    );
  }
}

export default async function AdminOffers({ searchParams }: AdminOffersProps) {
  return (
    <ErrorBoundary>
      <div className="p-6 max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Special Offers Management
            </h1>
            <p className="text-gray-600 mt-1">
              Manage packages, discounts, seasonal offers, and promotional deals
            </p>
          </div>
          <Link
            href="/admin/offers/create"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors shadow-sm"
            aria-label="Create new special offer"
          >
            <span className="mr-2">+</span>
            Add Special Offer
          </Link>
        </div>

        <Suspense
          fallback={
            <GridSkeleton
              count={6}
              columns={3}
              SkeletonComponent={AdminCardSkeleton}
            />
          }
        >
          <OffersGrid />
        </Suspense>
      </div>
    </ErrorBoundary>
  );
}
